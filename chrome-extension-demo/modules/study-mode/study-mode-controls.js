// Study Mode Controls Module - 播放器控制模块
console.log('Study Mode Controls module loaded');

// 全局变量
let progressUpdateInterval = null;
let customTooltip = null;
let heatMapData = null; // 缓存热力图数据

/**
 * 缩略图预览功能
 */

// 显示缩略图预览
async function showThumbnailPreview(timeInSeconds, videoElement, previewContainer) {
  // 检查storyboard模块是否可用
  if (!window.StudyModeStoryboard || !window.StudyModeStoryboard.isEnabled()) {
    return;
  }

  // 如果storyboard还未初始化，静默跳过
  if (!window.StudyModeStoryboard.hasConfig()) {
    return;
  }

  try {
    // 直接获取缩略图预览，不显示加载状态
    const thumbnailDataUrl = await window.StudyModeStoryboard.getPreviewAtTime(timeInSeconds, videoElement);
    
    if (thumbnailDataUrl) {
      // 显示缩略图和章节信息
      window.StudyModeStoryboard.showPreview(thumbnailDataUrl, previewContainer, timeInSeconds);
    }
    // 如果获取失败，不做任何操作，保持当前状态
  } catch (error) {
    // 静默处理错误，不影响用户体验
    console.warn('缩略图预览获取失败:', error);
  }
}

// 隐藏缩略图预览
function hideThumbnailPreview(previewContainer) {
  if (window.StudyModeStoryboard) {
    window.StudyModeStoryboard.hidePreview(previewContainer);
  }
}

/**
 * YouTube热力图功能
 */

// 检测是否有章节结构
function detectChapterStructure() {
  const chapterElements = document.querySelectorAll('.ytp-heat-map-chapter');
  return chapterElements.length > 0;
}

// 解析章节的位置和尺寸信息
function parseChapterDimensions(chapterElement) {
  const style = chapterElement.getAttribute('style') || '';
  
  // 解析width和left值
  const widthMatch = style.match(/width:\s*(\d+(?:\.\d+)?)px/);
  const leftMatch = style.match(/left:\s*(\d+(?:\.\d+)?)px/);
  
  const width = widthMatch ? parseFloat(widthMatch[1]) : 0;
  const left = leftMatch ? parseFloat(leftMatch[1]) : 0;
  
  return { width, left };
}

// 解析所有章节的热力图数据
function parseChapterHeatMaps() {
  const chapterElements = document.querySelectorAll('.ytp-heat-map-chapter');
  const allChapterData = [];
  
  console.log('发现', chapterElements.length, '个章节');
  
  // 计算总宽度
  let totalWidth = 0;
  const chapterInfos = [];
  
  chapterElements.forEach((chapterElement, index) => {
    const dimensions = parseChapterDimensions(chapterElement);
    chapterInfos.push({
      element: chapterElement,
      width: dimensions.width,
      left: dimensions.left,
      right: dimensions.left + dimensions.width
    });
    totalWidth = Math.max(totalWidth, dimensions.left + dimensions.width);
  });
  
  console.log('章节总宽度:', totalWidth, 'px');
  console.log('章节信息:', chapterInfos.map(info => ({
    width: info.width,
    left: info.left,
    timeStart: (info.left / totalWidth * 100).toFixed(1) + '%',
    timeEnd: (info.right / totalWidth * 100).toFixed(1) + '%'
  })));
  
  // 解析每个章节的热力图数据
  chapterInfos.forEach((chapterInfo, index) => {
    const svgElement = chapterInfo.element.querySelector('.ytp-heat-map-svg');
    const pathElement = svgElement?.querySelector('.ytp-heat-map-path');
    
    if (pathElement) {
      const pathData = pathElement.getAttribute('d');
      const chapterPoints = parseHeatMapPath(pathData);
      
      console.log(`章节${index + 1}解析到${chapterPoints.length}个数据点`);
      
      // 将章节内的相对时间映射到全局时间
      chapterPoints.forEach(point => {
        // 计算章节在全局的时间范围
        const chapterStartTime = chapterInfo.left / totalWidth;
        const chapterEndTime = chapterInfo.right / totalWidth;
        const chapterDuration = chapterEndTime - chapterStartTime;
        
        // 将点的时间从章节相对时间转换为全局时间
        const globalTime = chapterStartTime + (point.time * chapterDuration);
        
        allChapterData.push({
          time: globalTime,
          heat: point.heat,
          originalX: point.originalX,
          originalY: point.originalY,
          chapterIndex: index,
          chapterStartTime: chapterStartTime,
          chapterEndTime: chapterEndTime
        });
      });
    }
  });
  
  // 按时间排序
  allChapterData.sort((a, b) => a.time - b.time);
  
  console.log('合并后的章节数据点:', allChapterData.length, '个');
  console.log('前10个合并点:', allChapterData.slice(0, 10).map(p => ({
    time: (p.time * 100).toFixed(1) + '%',
    heat: (p.heat * 100).toFixed(1) + '%',
    chapter: p.chapterIndex + 1
  })));
  
  return allChapterData;
}

// 解析SVG路径数据，提取热力图坐标点
function parseHeatMapPath(pathData) {
  const points = [];
  
  try {
    // 解析SVG路径命令
    // 路径格式类似：M 0.0,100.0 C 7.1,89.8 14.3,54.3 35.7,48.9 C 57.1,43.5 78.6,66.8 107.1,72.9 ...
    
    // 首先分割路径命令
    const commands = pathData.split(/(?=[MCL])/);
    
    for (const command of commands) {
      const trimmed = command.trim();
      if (trimmed.startsWith('C ')) {
        // 解析贝塞尔曲线命令
        // C后面跟着6个数字：x1,y1 x2,y2 x3,y3
        const coordStr = trimmed.substring(2); // 移除'C '
        const coords = coordStr.match(/([+-]?\d*\.?\d+),([+-]?\d*\.?\d+)/g);
        
        if (coords && coords.length >= 3) {
          // 根据GitHub解释：每个C命令后的第三对坐标是有效数据点
          const thirdCoord = coords[2]; // 取第三对坐标
          const [x, y] = thirdCoord.split(',').map(parseFloat);
          
          // 检查x坐标是否符合规则：个位数为5的整数（5, 15, 25, 35...）
          if (Number.isInteger(x) && x % 10 === 5) {
            points.push({ x, y, originalX: x, originalY: y });
          }
        }
      } else if (trimmed.startsWith('M ')) {
        // 解析移动命令（起始点）
        const coordStr = trimmed.substring(2);
        const match = coordStr.match(/([+-]?\d*\.?\d+),([+-]?\d*\.?\d+)/);
        if (match) {
          const x = parseFloat(match[1]);
          const y = parseFloat(match[2]);
          // 起始点通常是(0, 100)，可以作为参考点
          if (x === 0) {
            points.push({ x, y, originalX: x, originalY: y });
          }
        }
      }
    }
    
    console.log('解析到的有效数据点:', points.length, '个');
    console.log('数据点详情:', points.slice(0, 10));
    
    // 如果按严格规则解析的点太少，使用宽松的解析策略
    if (points.length < 20) {
      console.log('严格解析点数太少，使用宽松策略...');
      const allPoints = [];
      
      // 重新解析，提取所有坐标点
      const allCoords = pathData.match(/([+-]?\d*\.?\d+),([+-]?\d*\.?\d+)/g);
      if (allCoords) {
        allCoords.forEach(coord => {
          const [x, y] = coord.split(',').map(parseFloat);
          allPoints.push({ x, y, originalX: x, originalY: y });
        });
        
        // 采样一些点作为热力图数据
        const step = Math.max(1, Math.floor(allPoints.length / 50));
        for (let i = 0; i < allPoints.length; i += step) {
          points.push(allPoints[i]);
        }
      }
    }
    
    // 转换坐标到0-1范围
    const normalizedPoints = points.map(point => ({
      time: Math.max(0, Math.min(1, (point.x - 5) / 995)), // 时间戳百分比：(x-5)/995，因为x从5到1000
      heat: Math.max(0, Math.min(1, (100 - point.y) / 100)), // 热度值
      originalX: point.x,
      originalY: point.y
    }));
    
    // 按时间排序
    normalizedPoints.sort((a, b) => a.time - b.time);
    
    console.log('最终标准化数据点:', normalizedPoints.length, '个');
    console.log('前5个标准化点:', normalizedPoints.slice(0, 5));
    
    return normalizedPoints;
    
  } catch (error) {
    console.warn('解析热力图路径数据失败:', error);
    return [];
  }
}

// 根据热力图数据创建新的SVG
function createHeatMapSVG(heatMapPoints) {
  if (!heatMapPoints || heatMapPoints.length === 0) {
    return null;
  }
  
  // 创建SVG元素
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('viewBox', '0 0 1000 100');
  svg.setAttribute('preserveAspectRatio', 'none');
  svg.style.width = '100%';
  svg.style.height = '100%';
  svg.classList.add('study-mode-heatmap-svg');
  
  // 创建渐变定义
  const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
  const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
  gradient.setAttribute('id', 'heatmap-gradient');
  gradient.setAttribute('x1', '0%');
  gradient.setAttribute('x2', '0%');
  gradient.setAttribute('y1', '0%');
  gradient.setAttribute('y2', '100%');
  
  const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
  stop1.setAttribute('offset', '0%');
  stop1.setAttribute('stop-color', 'white');
  stop1.setAttribute('stop-opacity', '0.8');
  
  const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
  stop2.setAttribute('offset', '100%');
  stop2.setAttribute('stop-color', 'white');
  stop2.setAttribute('stop-opacity', '0.2');
  
  gradient.appendChild(stop1);
  gradient.appendChild(stop2);
  defs.appendChild(gradient);
  svg.appendChild(defs);
  
  // 不再创建条形图，直接创建平滑曲线
  
  // 创建平滑的热力图曲线
  if (heatMapPoints.length > 1) {
    // 创建填充区域
    const fillPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    let pathData = `M 0,100`; // 从左下角开始
    
    // 添加所有数据点
    heatMapPoints.forEach(point => {
      const x = point.time * 1000;
      const y = 100 - (point.heat * 85); // 使用更大的范围，让变化更明显
      pathData += ` L ${x},${y}`;
    });
    
    // 闭合路径到右下角
    pathData += ` L 1000,100 Z`;
    
    fillPath.setAttribute('d', pathData);
    fillPath.setAttribute('fill', 'white');
    fillPath.setAttribute('opacity', '0.4');
    
    svg.appendChild(fillPath);
    
    // 创建边界曲线（不填充，只有描边）
    const strokePath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    let strokeData = `M 0,${100 - (heatMapPoints[0].heat * 85)}`; // 从第一个点开始
    
    heatMapPoints.forEach(point => {
      const x = point.time * 1000;
      const y = 100 - (point.heat * 85);
      strokeData += ` L ${x},${y}`;
    });
    
    strokePath.setAttribute('d', strokeData);
    strokePath.setAttribute('fill', 'none');
    strokePath.setAttribute('stroke', 'white');
    strokePath.setAttribute('stroke-width', '1.5');
    strokePath.setAttribute('opacity', '0.7');
    
    svg.appendChild(strokePath);
  }
  
  return svg;
}

// 获取YouTube热力图数据
function getYouTubeHeatMapData() {
  if (heatMapData) {
    return heatMapData; // 返回缓存的数据
  }
  
  try {
    // 首先检测是否有章节结构
    const hasChapters = detectChapterStructure();
    
    if (hasChapters) {
      console.log('检测到章节结构，使用章节热力图解析');
      
      // 解析所有章节的热力图数据
      const chapterHeatMapPoints = parseChapterHeatMaps();
      
      if (chapterHeatMapPoints.length > 0) {
        // 根据章节数据创建统一的热力图SVG
        const newSvg = createHeatMapSVG(chapterHeatMapPoints);
        if (newSvg) {
          heatMapData = newSvg;
          console.log('YouTube章节热力图数据获取成功，总数据点数量:', chapterHeatMapPoints.length);
          return heatMapData;
        }
      }
    } else {
      console.log('未检测到章节结构，使用单一热力图解析');
      
      // 从YouTube页面查找热力图SVG元素
      const heatMapSvg = document.querySelector('.ytp-heat-map-svg');
      if (heatMapSvg) {
        console.log('找到YouTube热力图SVG元素');
        
        // 查找路径元素
        const pathElement = heatMapSvg.querySelector('.ytp-heat-map-path');
        if (pathElement) {
          const pathData = pathElement.getAttribute('d');
          console.log('热力图路径数据:', pathData.substring(0, 100) + '...');
          
          // 解析路径数据
          const heatMapPoints = parseHeatMapPath(pathData);
          
          if (heatMapPoints.length > 0) {
            // 根据解析的数据创建新的SVG
            const newSvg = createHeatMapSVG(heatMapPoints);
            if (newSvg) {
              heatMapData = newSvg;
              console.log('YouTube热力图数据获取成功，数据点数量:', heatMapPoints.length);
              return heatMapData;
            }
          }
        }
        
        // 如果解析失败，回退到克隆原始SVG
        console.log('路径解析失败，使用原始SVG');
        const clonedSvg = heatMapSvg.cloneNode(true);
        clonedSvg.style.height = '100%';
        clonedSvg.style.width = '100%';
        clonedSvg.removeAttribute('class');
        clonedSvg.classList.add('study-mode-heatmap-svg');
        
        // 尝试修复原始SVG的显示问题
        const rects = clonedSvg.querySelectorAll('rect');
        rects.forEach(rect => {
          rect.setAttribute('fill', 'white');
          rect.setAttribute('fill-opacity', '0.6');
        });
        
        heatMapData = clonedSvg;
        console.log('YouTube热力图数据获取成功（使用原始SVG）');
        return heatMapData;
      }
    }
    
    console.log('未找到YouTube热力图数据');
    return null;
    
  } catch (error) {
    console.warn('获取YouTube热力图数据失败:', error);
    return null;
  }
}

// 显示热力图
function showHeatMap() {
  const heatMapContainer = document.getElementById('progress-heatmap');
  const heatMapContent = heatMapContainer?.querySelector('.heatmap-content');
  
  if (!heatMapContainer || !heatMapContent) return;
  
  // 获取热力图数据
  const heatMapSvg = getYouTubeHeatMapData();
  if (!heatMapSvg) {
    return; // 如果没有热力图数据，不显示
  }
  
  // 清空内容并添加热力图
  heatMapContent.innerHTML = '';
  heatMapContent.appendChild(heatMapSvg.cloneNode(true));
  
  // 显示容器
  heatMapContainer.style.display = 'block';
  heatMapContainer.style.opacity = '1';
}

// 隐藏热力图
function hideHeatMap() {
  const heatMapContainer = document.getElementById('progress-heatmap');
  if (heatMapContainer) {
    heatMapContainer.style.opacity = '0';
    // 延迟隐藏，提供平滑的过渡效果
    setTimeout(() => {
      if (heatMapContainer.style.opacity === '0') {
        heatMapContainer.style.display = 'none';
      }
    }, 200);
  }
}

/**
 * 进度条和控制按钮区域创建
 */

// 创建进度条和控制按钮区域
function createProgressAndControlsArea(overlay) {
  console.log('创建进度条和控制按钮区域');
  
  // 检查是否已经存在
  let progressContainer = document.getElementById('study-mode-progress-container');
  if (progressContainer) {
    progressContainer.style.display = 'block';
    return;
  }
  
  // 创建进度条和控制按钮容器
  progressContainer = document.createElement('div');
  progressContainer.id = 'study-mode-progress-container';
  progressContainer.className = 'study-mode-progress-container';
  
  progressContainer.innerHTML = `
    <div class="video-progress-section">
      <div class="progress-bar-container">
        <div class="progress-heatmap-container" id="progress-heatmap">
          <div class="heatmap-content"></div>
        </div>
        <div class="progress-bar-track">
          <div class="progress-bar-loaded"></div>
          <div class="progress-bar-played"></div>
          <div class="progress-bar-thumb"></div>
        </div>
        <div class="progress-preview" id="progress-preview">
          <div class="preview-thumbnail"></div>
          <div class="preview-chapter"></div>
          <div class="preview-time"></div>
        </div>
      </div>
    </div>
    <div class="video-controls-section">
      <button class="control-button" id="prev-button" data-tooltip="后退5秒">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="19,20 9,12 19,4"></polygon>
          <line x1="5" y1="19" x2="5" y2="5"></line>
        </svg>
      </button>
      <button class="control-button play-pause-button" id="play-pause-button" data-tooltip="播放/暂停">
        <svg class="play-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="5,3 19,12 5,21"></polygon>
        </svg>
        <svg class="pause-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
          <rect x="6" y="4" width="4" height="16"></rect>
          <rect x="14" y="4" width="4" height="16"></rect>
        </svg>
      </button>
      <button class="control-button" id="next-button" data-tooltip="前进5秒">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="5,4 15,12 5,20"></polygon>
          <line x1="19" y1="5" x2="19" y2="19"></line>
        </svg>
      </button>
      
      <div class="subtitle-controls-section">
        <button class="subtitle-control-button" id="analyze-btn" data-tooltip="语法分析">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.35-4.35"/>
            <path d="M11 8a3 3 0 1 0 0 6"/>
          </svg>
        </button>
        <button class="subtitle-control-button" id="tts-toggle" data-tooltip="中文朗读">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
            <path d="M15.54 8.46a5 5 0 0 1 0 7.07"/>
            <path d="M19.07 4.93a10 10 0 0 1 0 14.14"/>
          </svg>
        </button>
        <button class="subtitle-control-button" id="transcribe-btn" data-tooltip="开始字幕转录">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
        </button>
        <button class="subtitle-control-button" id="summary-btn" data-tooltip="视频摘要">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M8 6h13"/>
            <path d="M8 12h13"/>
            <path d="M8 18h13"/>
            <path d="M3 6h.01"/>
            <path d="M3 12h.01"/>
            <path d="M3 18h.01"/>
          </svg>
        </button>
        <button class="subtitle-control-button" id="mindmap-btn" data-tooltip="思维导图">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 12h3"/>
            <path d="M6 12v4h3"/>
            <path d="M6 12v-4h3"/>
            <path d="M9 16h3"/>
            <path d="M9 8h3"/>
            <path d="M12 16v2h3"/>
            <path d="M12 16v-2h3"/>
            <path d="M12 8v2h3"/>
            <path d="M12 8v-2h3"/>
          </svg>
        </button>
        <button class="subtitle-control-button" id="fullscreen-btn" data-tooltip="全屏模式">
          <svg class="enter-fullscreen-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
          </svg>
          <svg class="exit-fullscreen-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
            <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"/>
          </svg>
        </button>
      </div>
    </div>
  `;
  
  // 添加到覆盖层
  overlay.appendChild(progressContainer);
  
  // 创建自定义tooltip
  createCustomTooltip();
  
  // 设置进度条事件
  setupProgressBarEvents();
  
  // 设置控制按钮事件
  setupControlButtonEvents();
  
  // 设置字幕控制按钮事件
  setupSubtitleControlButtonEvents();
  
  // 设置tooltip事件
  setupTooltipEvents();

  // 设置全屏事件监听器
  setupFullscreenEventListeners();

  // 开始更新进度条
  startProgressUpdater();
}

/**
 * 自定义Tooltip功能
 */

// 创建自定义tooltip元素
function createCustomTooltip() {
  if (customTooltip) return;
  
  customTooltip = document.createElement('div');
  customTooltip.className = 'custom-tooltip';
  document.body.appendChild(customTooltip);
}

// 显示tooltip
function showTooltip(element, text) {
  if (!customTooltip || !text) return;
  
  customTooltip.textContent = text;
  customTooltip.classList.add('show');
  
  // 计算位置
  const rect = element.getBoundingClientRect();
  const tooltipRect = customTooltip.getBoundingClientRect();
  
  const left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
  const top = rect.top - tooltipRect.height - 8;
  
  // 确保tooltip不会超出屏幕边界
  const finalLeft = Math.max(8, Math.min(left, window.innerWidth - tooltipRect.width - 8));
  const finalTop = Math.max(8, top);
  
  customTooltip.style.left = finalLeft + 'px';
  customTooltip.style.top = finalTop + 'px';
}

// 隐藏tooltip
function hideTooltip() {
  if (customTooltip) {
    customTooltip.classList.remove('show');
  }
}

// 设置tooltip事件
function setupTooltipEvents() {
  const buttonsWithTooltip = document.querySelectorAll('[data-tooltip]');
  
  buttonsWithTooltip.forEach(button => {
    button.addEventListener('mouseenter', function() {
      // 对转录按钮做特殊处理，动态显示语言信息
      if (this.id === 'transcribe-btn') {
        // 实时读取全局语言设置
        chrome.storage.sync.get(['selectedVideoLanguage'], (result) => {
          const globalLanguage = result.selectedVideoLanguage || 'english';
          const languageName = globalLanguage === 'english' ? '英语' : '日语';
          const dynamicTooltip = `开始字幕转录（${languageName}）`;
          showTooltip(this, dynamicTooltip);
        });
      } else {
        // 其他按钮使用静态tooltip
        const tooltipText = this.getAttribute('data-tooltip');
        showTooltip(this, tooltipText);
      }
    });
    
    button.addEventListener('mouseleave', function() {
      hideTooltip();
    });
    
    // 在按钮被点击时也隐藏tooltip
    button.addEventListener('click', function() {
      hideTooltip();
    });
  });
}

/**
 * 进度条事件设置
 */

// 设置进度条事件
function setupProgressBarEvents() {
  const progressTrack = document.querySelector('.progress-bar-track');
  const progressPreview = document.getElementById('progress-preview');
  const previewThumbnail = progressPreview?.querySelector('.preview-thumbnail');
  const previewTime = progressPreview?.querySelector('.preview-time');
  
  if (!progressTrack || !progressPreview || !previewTime) return;
  
  let isHovering = false;
  let isDragging = false;
  
  // 鼠标移入进度条
  progressTrack.addEventListener('mouseenter', function() {
    isHovering = true;
    progressPreview.style.display = 'block';
    // 显示热力图
    showHeatMap();
  });
  
  // 鼠标移出进度条
  progressTrack.addEventListener('mouseleave', function() {
    if (!isDragging) {
      isHovering = false;
      progressPreview.style.display = 'none';
      // 隐藏缩略图预览
      hideThumbnailPreview(progressPreview);
      // 隐藏热力图
      hideHeatMap();
    }
  });
  
  // 鼠标在进度条上移动
  progressTrack.addEventListener('mousemove', function(e) {
    if (!isHovering && !isDragging) return;
    
    const rect = progressTrack.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    
    // 获取视频元素
    const videoElement = document.querySelector('video.html5-main-video');
    if (!videoElement) return;
    
    const previewTime_seconds = percent * videoElement.duration;
    
    // 更新预览位置 - 修复位置计算
    const mouseX = e.clientX - rect.left; // 鼠标相对于进度条的位置
    
    // 等待预览容器渲染完成后再计算位置
    requestAnimationFrame(() => {
      const previewWidth = progressPreview.offsetWidth || 160; // 默认宽度
      const halfWidth = previewWidth / 2;
      const maxLeft = rect.width - halfWidth;
      const minLeft = halfWidth;
      let newLeft = mouseX;
      if (newLeft < minLeft) newLeft = minLeft;
      if (newLeft > maxLeft) newLeft = maxLeft;
      progressPreview.style.left = newLeft + 'px';
    });
    
    // 更新预览时间
    previewTime.textContent = window.StudyModeUtils.formatTime(previewTime_seconds);
    
    // 显示缩略图预览
    showThumbnailPreview(previewTime_seconds, videoElement, progressPreview);
  });
  
  // 点击进度条跳转
  progressTrack.addEventListener('click', function(e) {
    const rect = progressTrack.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    
    const videoElement = document.querySelector('video.html5-main-video');
    if (videoElement) {
      videoElement.currentTime = percent * videoElement.duration;
    }
  });
  
  // 拖拽功能
  progressTrack.addEventListener('mousedown', function(e) {
    isDragging = true;
    document.addEventListener('mousemove', onDrag);
    document.addEventListener('mouseup', onDragEnd);
    e.preventDefault();
  });
  
  function onDrag(e) {
    if (!isDragging) return;
    
    const rect = progressTrack.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    
    const videoElement = document.querySelector('video.html5-main-video');
    if (videoElement) {
      videoElement.currentTime = percent * videoElement.duration;
    }
  }
  
  function onDragEnd() {
    isDragging = false;
    if (!isHovering) {
      progressPreview.style.display = 'none';
      // 隐藏热力图
      hideHeatMap();
    }
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mouseup', onDragEnd);
  }
}

/**
 * 控制按钮事件设置
 */

// 设置控制按钮事件
function setupControlButtonEvents() {
  const playPauseButton = document.getElementById('play-pause-button');
  const prevButton = document.getElementById('prev-button');
  const nextButton = document.getElementById('next-button');
  
  // 播放/暂停按钮
  if (playPauseButton) {
    playPauseButton.addEventListener('click', function() {
      const videoElement = document.querySelector('video.html5-main-video');
      if (!videoElement) return;
      
      if (videoElement.paused) {
        videoElement.play();
      } else {
        videoElement.pause();
      }
      
      updatePlayPauseButton();
    });
  }
  
  // 上一个按钮（后退5秒）
  if (prevButton) {
    prevButton.addEventListener('click', function() {
      const videoElement = document.querySelector('video.html5-main-video');
      if (videoElement) {
        videoElement.currentTime = Math.max(0, videoElement.currentTime - 5);
      }
    });
  }
  
  // 下一个按钮（前进5秒）
  if (nextButton) {
    nextButton.addEventListener('click', function() {
      const videoElement = document.querySelector('video.html5-main-video');
      if (videoElement) {
        videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 5);
      }
    });
  }
}

/**
 * Premium会员权限校验
 */

// 检查Premium会员权限的通用函数
async function checkPremiumPermission() {
  return new Promise((resolve) => {
    // 通过background.js代理请求检查权限
    chrome.runtime.sendMessage({
      action: 'checkPremiumPermission'
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('检查Premium权限失败:', chrome.runtime.lastError.message);
        resolve({
          success: false,
          isPremium: false,
          error: '权限检查失败',
          errorType: 'CHECK_FAILED'
        });
      } else {
        resolve(response);
      }
    });
  });
}

// 检查Plus或Premium会员权限的通用函数
async function checkPlusOrPremiumPermission() {
  return new Promise((resolve) => {
    // 通过background.js代理请求检查权限
    chrome.runtime.sendMessage({
      action: 'checkPlusOrPremiumPermission'
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('检查Plus/Premium权限失败:', chrome.runtime.lastError.message);
        resolve({
          success: false,
          isPlusOrPremium: false,
          error: '权限检查失败',
          errorType: 'CHECK_FAILED'
        });
      } else {
        resolve(response);
      }
    });
  });
}

// 显示升级提示对话框
function showUpgradeDialog(featureName, requiredMembership = 'Premium') {
  // 根据所需会员等级确定标题和文案
  const isPlus = requiredMembership === 'Plus';
  const title = isPlus ? '升级以解锁 Plus 功能' : '升级以解锁 Premium 功能';
  const description = isPlus ? 
    `<strong>${featureName}</strong> 需要 Plus 或 Premium 会员资格` :
    `<strong>${featureName}</strong> 是 Premium 会员专属功能`;
  
  // 创建升级提示对话框
  const dialog = document.createElement('div');
  dialog.className = 'premium-upgrade-dialog';
  dialog.innerHTML = `
    <div class="dialog-overlay"></div>
    <div class="dialog-content">
      <div class="dialog-header">
        <h3>${title}</h3>
        <button class="dialog-close">&times;</button>
      </div>
      <div class="dialog-body">
        <div class="premium-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="#FFD700" stroke="#FFB000" stroke-width="2"/>
          </svg>
        </div>
        <p class="feature-text">
          ${description}
        </p>
      </div>
      <div class="dialog-footer">
        <button class="btn-upgrade">立即升级</button>
      </div>
    </div>
  `;
  
  // 添加样式
  if (!document.getElementById('premium-dialog-styles')) {
    const styles = document.createElement('style');
    styles.id = 'premium-dialog-styles';
    styles.textContent = `
      .premium-upgrade-dialog {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2147483647; /* Use a very high z-index to ensure it's on top of all YouTube elements */
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .dialog-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(4px);
      }
      
      .dialog-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        max-width: 480px;
        width: 90%;
        max-height: 80vh;
        overflow: hidden;
        animation: dialogAppear 0.3s ease-out;
      }
      
      @keyframes dialogAppear {
        from {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.9);
        }
        to {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }
      
      .dialog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 24px 16px;
        border-bottom: 1px solid #e5e7eb;
      }
      
      .dialog-header h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #111827;
      }
      
      .dialog-close {
        background: none;
        border: none;
        font-size: 24px;
        color: #6b7280;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s;
      }
      
      .dialog-close:hover {
        background: #f3f4f6;
        color: #374151;
      }
      
      .dialog-body {
        padding: 24px;
        text-align: center;
      }
      
      .premium-icon {
        margin-bottom: 16px;
      }
      
      .feature-text {
        font-size: 16px;
        color: #374151;
        margin-bottom: 24px;
      }
      
      .dialog-footer {
        display: flex;
        gap: 12px;
        padding: 16px 24px 24px;
        justify-content: center;
      }
      
      .btn-upgrade {
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
      
      .btn-upgrade:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        transform: translateY(-1px);
      }
    `;
    document.head.appendChild(styles);
  }
  
  // 添加到页面
  document.body.appendChild(dialog);
  
  // 绑定事件
  const closeBtn = dialog.querySelector('.dialog-close');
  const upgradeBtn = dialog.querySelector('.btn-upgrade');
  const overlay = dialog.querySelector('.dialog-overlay');
  
  function closeDialog() {
    dialog.remove();
  }
  
  closeBtn.addEventListener('click', closeDialog);
  overlay.addEventListener('click', closeDialog);
  
  upgradeBtn.addEventListener('click', function() {
    // 打开升级页面
    chrome.runtime.sendMessage({
      action: 'openUpgradePage',
      url: 'http://localhost:8080/index.html#pricing'
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('打开升级页面失败:', chrome.runtime.lastError.message);
        window.open('http://localhost:8080/index.html#pricing', '_blank');
      } else {
        console.log('升级页面已打开');
      }
    });
    closeDialog();
  });
  
  // ESC键关闭
  const handleKeydown = (e) => {
    if (e.key === 'Escape') {
      closeDialog();
      document.removeEventListener('keydown', handleKeydown);
    }
  };
  document.addEventListener('keydown', handleKeydown);
}

/**
 * 字幕控制按钮事件设置
 */

// 设置字幕控制按钮事件
function setupSubtitleControlButtonEvents() {
  // 语法分析按钮 - 添加Plus/Premium权限校验
  const analyzeBtn = document.getElementById('analyze-btn');
  if (analyzeBtn) {
    analyzeBtn.addEventListener('click', async function() {
      // 检查Plus/Premium权限
      const permissionResult = await checkPlusOrPremiumPermission();
      
      if (!permissionResult.success || !permissionResult.isPlusOrPremium) {
        // 显示升级提示 - Plus会员或Premium会员都可以使用
        showUpgradeDialog('语法分析', 'Plus');
        return;
      }
      
      // 有权限，继续执行语法分析功能
      if (window.subtitleManager) {
        window.subtitleManager.analyzeCurrentSubtitle();
      }
    });
  }
  
  // TTS切换按钮 - 添加Plus/Premium权限校验
  const ttsToggle = document.getElementById('tts-toggle');
  if (ttsToggle) {
    ttsToggle.addEventListener('click', async function() {
      // 检查Plus/Premium权限
      const permissionResult = await checkPlusOrPremiumPermission();
      
      if (!permissionResult.success || !permissionResult.isPlusOrPremium) {
        // 显示升级提示 - Plus会员或Premium会员都可以使用
        showUpgradeDialog('中文朗读', 'Plus');
        return;
      }
      
      // 有权限，继续执行TTS功能
      if (window.subtitleManager) {
        const isEnabled = ttsToggle.classList.toggle('active');
        window.subtitleManager.toggleTTS(isEnabled);
        
        // 更新图标
        const svg = ttsToggle.querySelector('svg');
        if (isEnabled) {
          // 切换为音乐图标（激活状态）
          svg.innerHTML = `
            <path d="M9 18V5l12-2v13"/>
            <circle cx="6" cy="18" r="3"/>
            <circle cx="18" cy="16" r="3"/>
          `;
          ttsToggle.setAttribute('data-tooltip', '原声');
        } else {
          // 切换回音量图标（默认状态）
          svg.innerHTML = `
            <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
            <path d="M15.54 8.46a5 5 0 0 1 0 7.07"/>
            <path d="M19.07 4.93a10 10 0 0 1 0 14.14"/>
          `;
          ttsToggle.setAttribute('data-tooltip', '中文朗读');
        }
      }
    });
  }
  
  // 字幕转录按钮 - 添加Premium权限校验
  const transcribeBtn = document.getElementById('transcribe-btn');
  if (transcribeBtn) {
    transcribeBtn.addEventListener('click', async function() {
      // 检查Premium权限
      const permissionResult = await checkPremiumPermission();
      
      if (!permissionResult.success || !permissionResult.isPremium) {
        // 显示升级提示 - 仅限Premium会员
        showUpgradeDialog('字幕转录', 'Premium');
        return;
      }
      
      // 有权限，继续执行转录功能
      // 从Chrome Storage获取全局语言设置
      chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
        const globalLanguage = result.selectedVideoLanguage || 'english';
        
        // 将全局语言设置转换为转录语言代码
        const transcriptionLanguage = globalLanguage === 'english' ? 'en' : 'ja';
        
        console.log('使用全局语言设置进行转录:', globalLanguage, '-> 转录语言:', transcriptionLanguage);
        
        if (window.subtitleManager) {
          // 设置转录语言到字幕管理器
          window.subtitleManager.transcriptionLanguage = transcriptionLanguage;
          window.subtitleManager.startTranscription();
        }
      });
    });
  }
  
  // 视频摘要按钮 - 添加Premium权限校验
  const summaryBtn = document.getElementById('summary-btn');
  if (summaryBtn) {
    summaryBtn.addEventListener('click', async function() {
      // 检查Premium权限
      const permissionResult = await checkPremiumPermission();
      
      if (!permissionResult.success || !permissionResult.isPremium) {
        // 显示升级提示 - 仅限Premium会员
        showUpgradeDialog('视频摘要', 'Premium');
        return;
      }
      
      // 有权限，继续执行摘要功能
      if (window.subtitleManager) {
        window.subtitleManager.generateVideoSummary();
      }
    });
  }
  
  // 思维导图按钮 - 添加Plus/Premium权限校验
  const mindmapBtn = document.getElementById('mindmap-btn');
  if (mindmapBtn) {
    mindmapBtn.addEventListener('click', async function() {
      // 检查Plus/Premium权限
      const permissionResult = await checkPlusOrPremiumPermission();

      if (!permissionResult.success || !permissionResult.isPlusOrPremium) {
        // 显示升级提示 - Plus会员或Premium会员都可以使用
        showUpgradeDialog('思维导图', 'Plus');
        return;
      }

      // 有权限，继续执行思维导图功能
      if (window.subtitleManager) {
        window.subtitleManager.generateMindMap();
      }
    });
  }

  // 全屏按钮
  const fullscreenBtn = document.getElementById('fullscreen-btn');
  if (fullscreenBtn) {
    fullscreenBtn.addEventListener('click', function() {
      toggleFullscreen();
    });
  }
}

/**
 * 进度条更新
 */

// 开始进度条更新器
function startProgressUpdater() {
  // 清除可能存在的旧更新器
  if (progressUpdateInterval) {
    clearInterval(progressUpdateInterval);
  }
  
  progressUpdateInterval = setInterval(updateProgressBar, 100);
  
  // 保存到全局以便清理
  window.progressUpdateInterval = progressUpdateInterval;
}

// 更新进度条
function updateProgressBar() {
  const videoElement = document.querySelector('video.html5-main-video');
  const progressPlayed = document.querySelector('.progress-bar-played');
  const progressLoaded = document.querySelector('.progress-bar-loaded');
  const progressThumb = document.querySelector('.progress-bar-thumb');
  
  if (!videoElement || !progressPlayed) return;
  
  // 更新播放进度
  const playedPercent = (videoElement.currentTime / videoElement.duration) * 100;
  progressPlayed.style.width = playedPercent + '%';
  
  // 更新缓冲进度
  if (progressLoaded && videoElement.buffered.length > 0) {
    const bufferedPercent = (videoElement.buffered.end(videoElement.buffered.length - 1) / videoElement.duration) * 100;
    progressLoaded.style.width = bufferedPercent + '%';
  }
  
  // 更新拖拽点位置
  if (progressThumb) {
    progressThumb.style.left = playedPercent + '%';
  }
  
  // 更新播放/暂停按钮状态
  updatePlayPauseButton();
}

// 更新播放/暂停按钮状态
function updatePlayPauseButton() {
  const playPauseButton = document.getElementById('play-pause-button');
  const playIcon = playPauseButton?.querySelector('.play-icon');
  const pauseIcon = playPauseButton?.querySelector('.pause-icon');
  const videoElement = document.querySelector('video.html5-main-video');
  
  if (!playPauseButton || !playIcon || !pauseIcon || !videoElement) return;
  
  if (videoElement.paused) {
    playIcon.style.display = 'block';
    pauseIcon.style.display = 'none';
    playPauseButton.setAttribute('data-tooltip', '播放');
  } else {
    playIcon.style.display = 'none';
    pauseIcon.style.display = 'block';
    playPauseButton.setAttribute('data-tooltip', '暂停');
  }
}

/**
 * 全屏功能
 */

// 切换全屏模式
function toggleFullscreen() {
  const isFullscreen = document.fullscreenElement ||
                      document.webkitFullscreenElement ||
                      document.mozFullScreenElement ||
                      document.msFullscreenElement;

  if (isFullscreen) {
    // 退出全屏
    exitFullscreen();
  } else {
    // 进入全屏
    enterFullscreen();
  }
}

// 进入全屏模式
function enterFullscreen() {
  const documentElement = document.documentElement;

  if (documentElement.requestFullscreen) {
    documentElement.requestFullscreen();
  } else if (documentElement.webkitRequestFullscreen) {
    documentElement.webkitRequestFullscreen();
  } else if (documentElement.mozRequestFullScreen) {
    documentElement.mozRequestFullScreen();
  } else if (documentElement.msRequestFullscreen) {
    documentElement.msRequestFullscreen();
  }
}

// 退出全屏模式
function exitFullscreen() {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen();
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen();
  } else if (document.msExitFullscreen) {
    document.msExitFullscreen();
  }
}

// 更新全屏按钮状态
function updateFullscreenButton() {
  const fullscreenBtn = document.getElementById('fullscreen-btn');
  if (!fullscreenBtn) return;

  const isFullscreen = document.fullscreenElement ||
                      document.webkitFullscreenElement ||
                      document.mozFullScreenElement ||
                      document.msFullscreenElement;

  const enterIcon = fullscreenBtn.querySelector('.enter-fullscreen-icon');
  const exitIcon = fullscreenBtn.querySelector('.exit-fullscreen-icon');

  console.log('更新全屏按钮状态:', { isFullscreen, enterIcon: !!enterIcon, exitIcon: !!exitIcon });

  if (isFullscreen) {
    // 全屏模式：显示退出图标，隐藏进入图标
    if (enterIcon) {
      enterIcon.style.opacity = '0';
      enterIcon.style.pointerEvents = 'none';
    }
    if (exitIcon) {
      exitIcon.style.display = 'block';
      exitIcon.style.opacity = '1';
      exitIcon.style.pointerEvents = 'auto';
    }
    fullscreenBtn.setAttribute('data-tooltip', '退出全屏模式');
    // 全屏模式下调整播放器大小
    adjustPlayerSizeForFullscreen(true);
  } else {
    // 窗口模式：显示进入图标，隐藏退出图标
    if (enterIcon) {
      enterIcon.style.opacity = '1';
      enterIcon.style.pointerEvents = 'auto';
    }
    if (exitIcon) {
      exitIcon.style.opacity = '0';
      exitIcon.style.pointerEvents = 'none';
      // 延迟隐藏，避免闪烁
      setTimeout(() => {
        if (exitIcon.style.opacity === '0') {
          exitIcon.style.display = 'none';
        }
      }, 200);
    }
    fullscreenBtn.setAttribute('data-tooltip', '全屏模式');
    // 退出全屏时恢复播放器大小
    adjustPlayerSizeForFullscreen(false);
  }
}

// 调整播放器大小以适应全屏模式
function adjustPlayerSizeForFullscreen(isFullscreen) {
  const playerContainer = document.querySelector('.study-mode-player-container');
  const subtitleContainer = document.getElementById('extension-subtitle-container');
  const chatBubble = document.getElementById('study-mode-chat-bubble');
  const chatContainer = document.getElementById('study-mode-chat-container');

  if (!playerContainer) return;

  console.log('调整播放器大小:', { isFullscreen });

  if (isFullscreen) {
    // 全屏模式下适度增大播放器，保留字幕区域空间
    playerContainer.style.width = '85%';
    playerContainer.style.maxWidth = '1000px';
    playerContainer.style.margin = '25px auto 50px'; // 减少下边距到50px
    playerContainer.style.transform = 'scale(1.05)'; // 轻微放大

    // 同时调整字幕容器位置，确保有足够间距
    if (subtitleContainer) {
      subtitleContainer.style.top = 'calc(50% + 200px)'; // 调整为200px，减少间距
    }

    // 调整聊天区域位置以适应全屏模式
    adjustChatAreaForFullscreen(true);
  } else {
    // 退出全屏时恢复原始大小
    playerContainer.style.width = '80%';
    playerContainer.style.maxWidth = '800px';
    playerContainer.style.margin = '30px auto 10px';
    playerContainer.style.transform = 'scale(1)'; // 恢复原始缩放

    // 恢复字幕容器位置
    if (subtitleContainer) {
      subtitleContainer.style.top = ''; // 清除内联样式，使用CSS默认值
    }

    // 恢复聊天区域位置
    adjustChatAreaForFullscreen(false);
  }
}

// 调整聊天区域位置以适应全屏模式
function adjustChatAreaForFullscreen(isFullscreen) {
  const chatBubble = document.getElementById('study-mode-chat-bubble');
  const chatContainer = document.getElementById('study-mode-chat-container');

  console.log('调整聊天区域位置:', { isFullscreen });

  if (isFullscreen) {
    // 全屏模式下调整聊天气泡位置 - 移动到右下角
    if (chatBubble) {
      chatBubble.style.top = 'auto'; // 清除top定位
      chatBubble.style.bottom = '120px'; // 距离底部120px，避开字幕区域
      chatBubble.style.right = '50px'; // 增加右边距，避免被遮挡
      chatBubble.style.transform = 'translateY(0)'; // 移除垂直居中变换
      chatBubble.classList.add('fullscreen-mode');
    }

    // 全屏模式下调整聊天容器位置 - 从气泡上方展开
    if (chatContainer) {
      chatContainer.style.top = 'auto'; // 清除top定位
      chatContainer.style.bottom = '180px'; // 在气泡上方60px处展开
      chatContainer.style.right = '50px'; // 与聊天气泡对齐
      chatContainer.style.width = '350px'; // 稍微增加宽度，利用全屏空间
      chatContainer.style.maxHeight = '50vh'; // 限制最大高度，确保不会超出屏幕
      chatContainer.classList.add('fullscreen-mode');
    }
  } else {
    // 非全屏模式下恢复原始位置
    if (chatBubble) {
      chatBubble.style.top = '50%'; // 恢复垂直居中
      chatBubble.style.bottom = 'auto'; // 清除bottom定位
      chatBubble.style.right = '30px'; // 恢复原始右边距
      chatBubble.style.transform = 'translateY(-50%)'; // 恢复垂直居中变换
      chatBubble.classList.remove('fullscreen-mode');
    }

    if (chatContainer) {
      chatContainer.style.top = '50px'; // 恢复原始顶部位置
      chatContainer.style.bottom = 'auto'; // 清除bottom定位
      chatContainer.style.right = 'calc(10vw - 20px)'; // 恢复原始右边距
      chatContainer.style.width = '325px'; // 恢复原始宽度
      chatContainer.style.maxHeight = ''; // 清除最大高度限制
      chatContainer.classList.remove('fullscreen-mode');
    }
  }

  // 调整聊天容器高度以适应新的布局
  if (window.StudyModeChat && window.StudyModeChat.adjustChatContainerHeight) {
    // 延迟调用，确保DOM更新完成
    setTimeout(() => {
      window.StudyModeChat.adjustChatContainerHeight();
    }, 100);
  }
}

// 设置全屏事件监听器
function setupFullscreenEventListeners() {
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', updateFullscreenButton);
  document.addEventListener('webkitfullscreenchange', updateFullscreenButton);
  document.addEventListener('mozfullscreenchange', updateFullscreenButton);
  document.addEventListener('MSFullscreenChange', updateFullscreenButton);
}

/**
 * 调试功能
 */

// 调试热力图数据解析
function debugHeatMapData() {
  console.log('=== 热力图调试信息 ===');
  
  // 首先检测章节结构
  const hasChapters = detectChapterStructure();
  console.log('章节检测结果:', hasChapters ? '✅ 有章节' : '❌ 无章节');
  
  if (hasChapters) {
    console.log('--- 章节热力图调试 ---');
    
    const chapterElements = document.querySelectorAll('.ytp-heat-map-chapter');
    console.log('章节数量:', chapterElements.length);
    
    chapterElements.forEach((chapter, index) => {
      const dimensions = parseChapterDimensions(chapter);
      const svgElement = chapter.querySelector('.ytp-heat-map-svg');
      const pathElement = svgElement?.querySelector('.ytp-heat-map-path');
      
      console.log(`章节 ${index + 1}:`);
      console.log(`  位置: left=${dimensions.left}px, width=${dimensions.width}px`);
      
      if (pathElement) {
        const pathData = pathElement.getAttribute('d');
        const points = parseHeatMapPath(pathData);
        console.log(`  路径长度: ${pathData.length}`);
        console.log(`  数据点数: ${points.length}`);
        
        if (points.length > 0) {
          const maxHeat = Math.max(...points.map(p => p.heat));
          const minHeat = Math.min(...points.map(p => p.heat));
          console.log(`  热度范围: ${(minHeat * 100).toFixed(1)}% - ${(maxHeat * 100).toFixed(1)}%`);
        }
      } else {
        console.log('  ❌ 未找到路径数据');
      }
    });
    
    // 解析合并后的数据
    const allChapterData = parseChapterHeatMaps();
    console.log('--- 合并后数据统计 ---');
    console.log('总数据点数:', allChapterData.length);
    
    if (allChapterData.length > 0) {
      const maxHeat = Math.max(...allChapterData.map(p => p.heat));
      const minHeat = Math.min(...allChapterData.map(p => p.heat));
      const avgHeat = allChapterData.reduce((sum, p) => sum + p.heat, 0) / allChapterData.length;
      
      console.log('合并后热度统计:');
      console.log(`  最高: ${(maxHeat * 100).toFixed(1)}%`);
      console.log(`  最低: ${(minHeat * 100).toFixed(1)}%`);
      console.log(`  平均: ${(avgHeat * 100).toFixed(1)}%`);
      
      console.log('前5个合并数据点:');
      allChapterData.slice(0, 5).forEach((point, index) => {
        console.log(`  ${index + 1}. 时间: ${(point.time * 100).toFixed(1)}%, 热度: ${(point.heat * 100).toFixed(1)}%, 章节: ${point.chapterIndex + 1}`);
      });
    }
    
  } else {
    console.log('--- 单一热力图调试 ---');
    
    const heatMapSvg = document.querySelector('.ytp-heat-map-svg');
    if (!heatMapSvg) {
      console.log('❌ 未找到YouTube热力图SVG元素');
      return;
    }
    
    console.log('✅ 找到热力图SVG元素');
    console.log('SVG viewBox:', heatMapSvg.getAttribute('viewBox'));
    console.log('SVG子元素数量:', heatMapSvg.children.length);
    
    const pathElement = heatMapSvg.querySelector('.ytp-heat-map-path');
    if (!pathElement) {
      console.log('❌ 未找到热力图路径元素');
      return;
    }
    
    const pathData = pathElement.getAttribute('d');
    console.log('✅ 找到路径数据，长度:', pathData.length);
    console.log('路径数据预览:', pathData.substring(0, 200) + '...');
    
    // 显示路径中的C命令数量
    const cCommands = pathData.split(/C\s+/).length - 1;
    console.log('路径中的C命令数量:', cCommands);
    
    // 解析数据点
    const points = parseHeatMapPath(pathData);
    console.log('解析结果:', points.length, '个数据点');
    
    if (points.length > 0) {
      console.log('前5个数据点:');
      points.slice(0, 5).forEach((point, index) => {
        console.log(`  ${index + 1}. 时间: ${(point.time * 100).toFixed(1)}%, 热度: ${(point.heat * 100).toFixed(1)}%`);
      });
      
      // 计算统计信息
      const maxHeat = Math.max(...points.map(p => p.heat));
      const minHeat = Math.min(...points.map(p => p.heat));
      const avgHeat = points.reduce((sum, p) => sum + p.heat, 0) / points.length;
      
      console.log('热度统计:');
      console.log(`  最高: ${(maxHeat * 100).toFixed(1)}%`);
      console.log(`  最低: ${(minHeat * 100).toFixed(1)}%`);
      console.log(`  平均: ${(avgHeat * 100).toFixed(1)}%`);
    }
  }
  
  console.log('=== 调试信息结束 ===');
}

// 测试特定SVG数据的解析
function testSpecificSVGData() {
  const testPathData = `M 0.0,100.0 C 1.0,92.8 2.0,71.2 5.0,64.0 C 8.0,56.8 11.0,65.8 15.0,63.8 C 19.0,61.8 21.0,56.2 25.0,54.1 C 29.0,52.0 31.0,52.2 35.0,53.2 C 39.0,54.3 41.0,58.8 45.0,59.4 C 49.0,60.1 51.0,58.9 55.0,56.6 C 59.0,54.4 61.0,48.9 65.0,48.3 C 69.0,47.7 71.0,51.5 75.0,53.7 C 79.0,55.9 81.0,59.0 85.0,59.2 C 89.0,59.4 91.0,56.5 95.0,54.7`;
  
  console.log('=== 测试特定SVG数据解析 ===');
  console.log('测试路径数据:', testPathData.substring(0, 100) + '...');
  
  const points = parseHeatMapPath(testPathData);
  console.log('解析到的点数:', points.length);
  
  points.forEach((point, index) => {
    console.log(`点${index + 1}: x=${point.originalX}, y=${point.originalY}, 时间=${(point.time * 100).toFixed(1)}%, 热度=${(point.heat * 100).toFixed(1)}%`);
  });
  
  console.log('=== 测试结束 ===');
}

/**
 * 清理功能
 */

// 清理进度条和控制按钮
function cleanupProgressAndControls() {
  // 清除进度更新器
  if (progressUpdateInterval) {
    clearInterval(progressUpdateInterval);
    progressUpdateInterval = null;
  }
  
  if (window.progressUpdateInterval) {
    clearInterval(window.progressUpdateInterval);
    window.progressUpdateInterval = null;
  }
  
  // 移除全屏事件监听器
  document.removeEventListener('fullscreenchange', updateFullscreenButton);
  document.removeEventListener('webkitfullscreenchange', updateFullscreenButton);
  document.removeEventListener('mozfullscreenchange', updateFullscreenButton);
  document.removeEventListener('MSFullscreenChange', updateFullscreenButton);
  
  // 清理自定义tooltip
  if (customTooltip) {
    customTooltip.remove();
    customTooltip = null;
  }
  
  // 清理热力图缓存数据
  heatMapData = null;
  
  // 移除进度容器，而不仅仅是隐藏
  const progressContainer = document.getElementById('study-mode-progress-container');
  if (progressContainer) {
    progressContainer.remove();
  }
  
  console.log('进度条和控制按钮已清理');
}

// 导出控制模块功能，供其他模块使用
window.StudyModeControls = {
  // UI创建
  createProgressAndControlsArea,
  
  // 进度条事件
  setupProgressBarEvents,
  
  // 控制按钮事件
  setupControlButtonEvents,
  setupSubtitleControlButtonEvents,
  
  // Tooltip功能
  createCustomTooltip,
  showTooltip,
  hideTooltip,
  setupTooltipEvents,
  
  // 缩略图预览功能
  showThumbnailPreview,
  hideThumbnailPreview,
  
  // 热力图功能
  detectChapterStructure,
  parseChapterDimensions,
  parseChapterHeatMaps,
  parseHeatMapPath,
  createHeatMapSVG,
  getYouTubeHeatMapData,
  showHeatMap,
  hideHeatMap,
  debugHeatMapData,
  testSpecificSVGData,
  
  // 进度条更新
  startProgressUpdater,
  updateProgressBar,
  updatePlayPauseButton,
  
  // 全屏功能
  toggleFullscreen,
  enterFullscreen,
  exitFullscreen,
  updateFullscreenButton,
  adjustPlayerSizeForFullscreen,
  adjustChatAreaForFullscreen,
  setupFullscreenEventListeners,
  
  // 权限校验
  checkPremiumPermission,
  checkPlusOrPremiumPermission,
  showUpgradeDialog,
  // 为了向后兼容，保留旧函数名
  showPremiumUpgradeDialog: showUpgradeDialog,
  
  // 清理功能
  cleanupProgressAndControls
}; 